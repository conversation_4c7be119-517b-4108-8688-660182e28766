import React, { useState, useEffect, useCallback } from 'react';
import {
  View,
  StyleSheet,
  ScrollView,
  Alert,
} from 'react-native';
import { router, useLocalSearchParams } from 'expo-router';
import { useForm, Controller } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';
import Toast from 'react-native-toast-message';

import { Colors } from '@/constants/Colors';
import { useColorScheme } from '@/hooks/useColorScheme';
import { apiHelpers } from '@/config/api';
import {
  Card,
  CardContent,
  CardHeader,
  Typography,
  Heading2,
  Button,
  Input,
  StatusBadge,
  HealthBadge,
} from '@/components/ui';

const checkupSchema = yup.object().shape({
  gula_darah: yup
    .number()
    .required('Gula darah wajib diisi')
    .min(30, '<PERSON>lai gula darah terlalu rendah')
    .max(600, '<PERSON><PERSON> gula darah terlalu tinggi'),
  tekanan_darah_sistolik: yup
    .number()
    .required('Tekanan darah sistolik wajib diisi')
    .min(60, 'Tekanan sistolik terlalu rendah')
    .max(250, 'Tekanan sistolik terlalu tinggi'),
  tekanan_darah_diastolik: yup
    .number()
    .required('Tekanan darah diastolik wajib diisi')
    .min(40, 'Tekanan diastolik terlalu rendah')
    .max(150, 'Tekanan diastolik terlalu tinggi'),
  berat_badan: yup
    .number()
    .required('Berat badan wajib diisi')
    .min(20, 'Berat badan terlalu rendah')
    .max(300, 'Berat badan terlalu tinggi'),
  tinggi_badan: yup
    .number()
    .required('Tinggi badan wajib diisi')
    .min(100, 'Tinggi badan terlalu rendah')
    .max(250, 'Tinggi badan terlalu tinggi'),
  keluhan: yup
    .string()
    .nullable()
    .default(''),
  catatan: yup
    .string()
    .nullable()
    .default(''),
});

type CheckupFormData = yup.InferType<typeof checkupSchema>;

interface ProfileData {
  id: string;
  nama: string;
  usia: number;
  alamat: string;
  no_telepon: string;
}

export default function AddCheckupScreen() {
  const colorScheme = useColorScheme();
  const colors = Colors[colorScheme ?? 'light'];
  const { profileId } = useLocalSearchParams<{ profileId: string }>();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [profileData, setProfileData] = useState<ProfileData | null>(null);
  const [isLoadingProfile, setIsLoadingProfile] = useState(true);

  const {
    control,
    handleSubmit,
    formState: { errors },
    watch,
    reset,
  } = useForm<CheckupFormData>({
    resolver: yupResolver(checkupSchema),
    defaultValues: {
      gula_darah: 0,
      tekanan_darah_sistolik: 0,
      tekanan_darah_diastolik: 0,
      berat_badan: 0,
      tinggi_badan: 0,
      keluhan: '',
      catatan: '',
    },
  });

  // Watch form values for real-time health status
  const watchedValues = watch();

  const loadProfileData = useCallback(async () => {
    setIsLoadingProfile(true);
    try {
      const response = await apiHelpers.getProfileById(profileId);

      if (response.success && response.data) {
        setProfileData(response.data);
      } else {
        Toast.show({
          type: 'error',
          text1: 'Gagal Memuat Profil',
          text2: response.message || 'Profil tidak ditemukan',
        });
        router.back();
      }
    } catch (error: any) {
      console.error('Profile load error:', error);
      Toast.show({
        type: 'error',
        text1: 'Gagal Memuat Profil',
        text2: error.response?.data?.message || 'Terjadi kesalahan',
      });
      router.back();
    } finally {
      setIsLoadingProfile(false);
    }
  }, [profileId]);

  useEffect(() => {
    if (profileId) {
      loadProfileData();
    }
  }, [profileId, loadProfileData]);

  const calculateBMI = (weight: number, height: number): number => {
    if (weight <= 0 || height <= 0) return 0;
    const heightInMeters = height / 100;
    return weight / (heightInMeters * heightInMeters);
  };

  const getBMIStatus = (bmi: number): 'good' | 'warning' | 'danger' | 'neutral' => {
    if (bmi === 0) return 'neutral';
    if (bmi < 18.5) return 'warning';
    if (bmi >= 18.5 && bmi < 25) return 'good';
    if (bmi >= 25 && bmi < 30) return 'warning';
    return 'danger';
  };

  const getBMILabel = (bmi: number): string => {
    if (bmi === 0) return 'Belum dihitung';
    if (bmi < 18.5) return 'Kurus';
    if (bmi >= 18.5 && bmi < 25) return 'Normal';
    if (bmi >= 25 && bmi < 30) return 'Gemuk';
    return 'Obesitas';
  };

  const onSubmit = async (data: CheckupFormData) => {
    if (!profileId) {
      Toast.show({
        type: 'error',
        text1: 'Error',
        text2: 'ID profil tidak ditemukan',
      });
      return;
    }

    setIsSubmitting(true);
    try {
      const checkupData = {
        ...data,
        profile_id: profileId,
      };

      const response = await apiHelpers.createCheckup(checkupData);
      
      if (response.success) {
        Toast.show({
          type: 'success',
          text1: 'Pemeriksaan Berhasil Disimpan',
          text2: `Data pemeriksaan ${profileData?.nama} telah tersimpan`,
        });
        
        // Show success alert with options
        Alert.alert(
          'Pemeriksaan Berhasil Disimpan',
          `Data pemeriksaan ${profileData?.nama} telah berhasil disimpan.`,
          [
            {
              text: 'Tambah Pemeriksaan Lagi',
              onPress: () => reset(),
            },
            {
              text: 'Lihat Riwayat',
              onPress: () => router.replace(`/profile/${profileId}/history`),
            },
            {
              text: 'Kembali ke Profil',
              onPress: () => router.replace(`/profile/${profileId}`),
              style: 'cancel',
            },
          ]
        );
      } else {
        Toast.show({
          type: 'error',
          text1: 'Gagal Menyimpan Pemeriksaan',
          text2: response.message || 'Terjadi kesalahan saat menyimpan data',
        });
      }
    } catch (error: any) {
      console.error('Checkup creation error:', error);
      Toast.show({
        type: 'error',
        text1: 'Gagal Menyimpan Pemeriksaan',
        text2: error.response?.data?.message || 'Terjadi kesalahan',
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  if (isLoadingProfile) {
    return (
      <View style={[styles.container, { backgroundColor: colors.background }]}>
        <View style={styles.loadingContainer}>
          <Typography variant="body1" align="center">
            Memuat data profil...
          </Typography>
        </View>
      </View>
    );
  }

  if (!profileData) {
    return (
      <View style={[styles.container, { backgroundColor: colors.background }]}>
        <View style={styles.errorContainer}>
          <Typography variant="body1" align="center" color="muted">
            Profil tidak ditemukan
          </Typography>
          <Button
            title="Kembali"
            onPress={() => router.back()}
            style={styles.backButton}
          />
        </View>
      </View>
    );
  }

  const currentBMI = calculateBMI(watchedValues.berat_badan, watchedValues.tinggi_badan);

  return (
    <View style={[styles.container, { backgroundColor: colors.background }]}>
      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={styles.scrollContent}
        keyboardShouldPersistTaps="handled"
      >
        <View style={styles.header}>
          <Heading2 weight="bold">Pemeriksaan Kesehatan</Heading2>
          <Typography variant="body2" color="muted">
            Tambah data pemeriksaan untuk {profileData.nama}
          </Typography>
        </View>

        {/* Profile Info */}
        <Card variant="outlined" style={styles.profileCard}>
          <CardContent>
            <View style={styles.profileInfo}>
              <Typography variant="h4" weight="semibold">
                {profileData.nama}
              </Typography>
              <Typography variant="body2" color="muted">
                {profileData.usia} tahun • {profileData.alamat}
              </Typography>
            </View>
          </CardContent>
        </Card>

        {/* Vital Signs */}
        <Card variant="outlined" style={styles.formCard}>
          <CardHeader>
            <Typography variant="h4" weight="semibold">
              Tanda Vital
            </Typography>
          </CardHeader>
          <CardContent>
            <View style={styles.formSection}>
              <Controller
                control={control}
                name="gula_darah"
                render={({ field: { onChange, onBlur, value } }) => (
                  <View>
                    <Input
                      label="Gula Darah (mg/dL)"
                      placeholder="Masukkan kadar gula darah"
                      value={value?.toString() || ''}
                      onChangeText={(text) => onChange(parseInt(text) || 0)}
                      onBlur={onBlur}
                      keyboardType="numeric"
                      error={errors.gula_darah?.message}
                      required
                    />
                    {value > 0 && (
                      <View style={styles.healthIndicator}>
                        <HealthBadge value={value} type="blood_sugar" />
                      </View>
                    )}
                  </View>
                )}
              />

              <View style={styles.bloodPressureContainer}>
                <Typography variant="body2" weight="medium" style={styles.sectionLabel}>
                  Tekanan Darah (mmHg) *
                </Typography>
                <View style={styles.bloodPressureInputs}>
                  <Controller
                    control={control}
                    name="tekanan_darah_sistolik"
                    render={({ field: { onChange, onBlur, value } }) => (
                      <Input
                        label="Sistolik"
                        placeholder="120"
                        value={value?.toString() || ''}
                        onChangeText={(text) => onChange(parseInt(text) || 0)}
                        onBlur={onBlur}
                        keyboardType="numeric"
                        error={errors.tekanan_darah_sistolik?.message}
                        containerStyle={styles.bloodPressureInput}
                      />
                    )}
                  />
                  <Typography variant="h4" style={styles.bloodPressureSeparator}>
                    /
                  </Typography>
                  <Controller
                    control={control}
                    name="tekanan_darah_diastolik"
                    render={({ field: { onChange, onBlur, value } }) => (
                      <Input
                        label="Diastolik"
                        placeholder="80"
                        value={value?.toString() || ''}
                        onChangeText={(text) => onChange(parseInt(text) || 0)}
                        onBlur={onBlur}
                        keyboardType="numeric"
                        error={errors.tekanan_darah_diastolik?.message}
                        containerStyle={styles.bloodPressureInput}
                      />
                    )}
                  />
                </View>
                {watchedValues.tekanan_darah_sistolik > 0 && watchedValues.tekanan_darah_diastolik > 0 && (
                  <View style={styles.healthIndicator}>
                    <HealthBadge
                      value={0}
                      type="blood_pressure"
                      systolic={watchedValues.tekanan_darah_sistolik}
                      diastolic={watchedValues.tekanan_darah_diastolik}
                    />
                  </View>
                )}
              </View>
            </View>
          </CardContent>
        </Card>

        {/* Physical Measurements */}
        <Card variant="outlined" style={styles.formCard}>
          <CardHeader>
            <Typography variant="h4" weight="semibold">
              Pengukuran Fisik
            </Typography>
          </CardHeader>
          <CardContent>
            <View style={styles.formSection}>
              <Controller
                control={control}
                name="berat_badan"
                render={({ field: { onChange, onBlur, value } }) => (
                  <Input
                    label="Berat Badan (kg)"
                    placeholder="Masukkan berat badan"
                    value={value?.toString() || ''}
                    onChangeText={(text) => onChange(parseFloat(text) || 0)}
                    onBlur={onBlur}
                    keyboardType="numeric"
                    error={errors.berat_badan?.message}
                    required
                  />
                )}
              />

              <Controller
                control={control}
                name="tinggi_badan"
                render={({ field: { onChange, onBlur, value } }) => (
                  <Input
                    label="Tinggi Badan (cm)"
                    placeholder="Masukkan tinggi badan"
                    value={value?.toString() || ''}
                    onChangeText={(text) => onChange(parseFloat(text) || 0)}
                    onBlur={onBlur}
                    keyboardType="numeric"
                    error={errors.tinggi_badan?.message}
                    required
                  />
                )}
              />

              {/* BMI Indicator */}
              {currentBMI > 0 && (
                <View style={styles.bmiContainer}>
                  <Typography variant="body2" weight="medium" color="muted">
                    Indeks Massa Tubuh (BMI)
                  </Typography>
                  <View style={styles.bmiIndicator}>
                    <Typography variant="h3" weight="bold">
                      {currentBMI.toFixed(1)}
                    </Typography>
                    <StatusBadge
                      status={getBMIStatus(currentBMI)}
                      label={getBMILabel(currentBMI)}
                    />
                  </View>
                </View>
              )}
            </View>
          </CardContent>
        </Card>

        {/* Additional Information */}
        <Card variant="outlined" style={styles.formCard}>
          <CardHeader>
            <Typography variant="h4" weight="semibold">
              Informasi Tambahan
            </Typography>
          </CardHeader>
          <CardContent>
            <View style={styles.formSection}>
              <Controller
                control={control}
                name="keluhan"
                render={({ field: { onChange, onBlur, value } }) => (
                  <Input
                    label="Keluhan"
                    placeholder="Masukkan keluhan pasien (opsional)"
                    value={value || ''}
                    onChangeText={onChange}
                    onBlur={onBlur}
                    multiline
                    numberOfLines={3}
                    error={errors.keluhan?.message}
                  />
                )}
              />

              <Controller
                control={control}
                name="catatan"
                render={({ field: { onChange, onBlur, value } }) => (
                  <Input
                    label="Catatan Petugas"
                    placeholder="Masukkan catatan pemeriksaan (opsional)"
                    value={value || ''}
                    onChangeText={onChange}
                    onBlur={onBlur}
                    multiline
                    numberOfLines={3}
                    error={errors.catatan?.message}
                  />
                )}
              />
            </View>
          </CardContent>
        </Card>

        <View style={styles.buttonContainer}>
          <Button
            title="Simpan Pemeriksaan"
            onPress={handleSubmit(onSubmit)}
            loading={isSubmitting}
            style={styles.submitButton}
          />
          <Button
            title="Batal"
            variant="outline"
            onPress={() => router.back()}
            disabled={isSubmitting}
            style={styles.cancelButton}
          />
        </View>
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    padding: 20,
    paddingBottom: 100,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    gap: 16,
  },
  backButton: {
    minWidth: 120,
  },
  header: {
    marginBottom: 24,
  },
  profileCard: {
    marginBottom: 20,
  },
  profileInfo: {
    gap: 4,
  },
  formCard: {
    marginBottom: 20,
  },
  formSection: {
    gap: 16,
  },
  healthIndicator: {
    marginTop: 8,
    alignItems: 'flex-start',
  },
  bloodPressureContainer: {
    gap: 8,
  },
  sectionLabel: {
    marginBottom: 4,
  },
  bloodPressureInputs: {
    flexDirection: 'row',
    alignItems: 'flex-end',
    gap: 12,
  },
  bloodPressureInput: {
    flex: 1,
  },
  bloodPressureSeparator: {
    marginBottom: 8,
    color: '#666',
  },
  bmiContainer: {
    padding: 16,
    borderRadius: 8,
    backgroundColor: 'rgba(59, 130, 246, 0.05)',
    gap: 8,
  },
  bmiIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  buttonContainer: {
    gap: 12,
    marginTop: 20,
  },
  submitButton: {
    marginBottom: 8,
  },
  cancelButton: {
    marginBottom: 8,
  },
});
