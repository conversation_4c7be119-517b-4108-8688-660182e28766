import React, { useState, useEffect, useCallback } from 'react';
import {
  View,
  StyleSheet,
  Modal,
  Share,
} from 'react-native';
import { printToFileAsync } from 'expo-print';
import { shareAsync } from 'expo-sharing';
import QRCode from 'react-native-qrcode-svg';
import { Ionicons } from '@expo/vector-icons';
import Toast from 'react-native-toast-message';

import { Colors } from '@/constants/Colors';
import { useColorScheme } from '@/hooks/useColorScheme';
import { apiHelpers } from '@/config/api';
import {
  <PERSON><PERSON>,
  Card,
  CardContent,
  CardHeader,
  Typography,
  Heading3,
} from '@/components/ui';



interface QRGeneratorProps {
  visible: boolean;
  onClose: () => void;
  profileId: string;
  profileName: string;
}

interface QRData {
  qr_code: string;
  qr_data: any;
  profile: any;
}

export const QRGenerator: React.FC<QRGeneratorProps> = ({
  visible,
  onClose,
  profileId,
  profileName,
}) => {
  const colorScheme = useColorScheme();
  const colors = Colors[colorScheme ?? 'light'];
  const [qrData, setQrData] = useState<QRData | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [qrRef, setQrRef] = useState<any>(null);

  const fetchQRData = useCallback(async () => {
    setIsLoading(true);
    try {
      const response = await apiHelpers.getProfileQR(profileId);
      
      if (response.success && response.data) {
        setQrData(response.data);
      } else {
        Toast.show({
          type: 'error',
          text1: 'Gagal Generate QR Code',
          text2: response.message || 'Tidak dapat membuat QR code',
        });
      }
    } catch (error: any) {
      console.error('QR generation error:', error);
      Toast.show({
        type: 'error',
        text1: 'Gagal Generate QR Code',
        text2: error.response?.data?.message || 'Terjadi kesalahan',
      });
    } finally {
      setIsLoading(false);
    }
  }, [profileId]);

  useEffect(() => {
    if (visible && profileId) {
      fetchQRData();
    }
  }, [visible, profileId, fetchQRData]);

  const handleShare = async () => {
    try {
      if (!qrData) return;

      const result = await Share.share({
        message: `QR Code untuk ${profileName}\n\nKode: ${qrData.qr_code}`,
        title: `QR Code - ${profileName}`,
      });

      if (result.action === Share.sharedAction) {
        Toast.show({
          type: 'success',
          text1: 'QR Code Dibagikan',
          text2: 'QR code berhasil dibagikan',
        });
      }
    } catch (error) {
      console.error('Share error:', error);
      Toast.show({
        type: 'error',
        text1: 'Gagal Membagikan',
        text2: 'Tidak dapat membagikan QR code',
      });
    }
  };

  const handlePrint = async () => {
    try {
      if (!qrData || !qrRef) return;

      // Get QR code as base64
      qrRef.toDataURL((dataURL: string) => {
        generatePDF(dataURL);
      });
    } catch (error) {
      console.error('Print error:', error);
      Toast.show({
        type: 'error',
        text1: 'Gagal Mencetak',
        text2: 'Tidak dapat mencetak QR code',
      });
    }
  };

  const generatePDF = async (qrDataURL: string) => {
    try {
      const htmlContent = `
        <!DOCTYPE html>
        <html>
        <head>
          <meta charset="utf-8">
          <title>QR Code - ${profileName}</title>
          <style>
            body {
              font-family: Arial, sans-serif;
              padding: 20px;
              text-align: center;
            }
            .header {
              margin-bottom: 30px;
            }
            .qr-container {
              margin: 30px 0;
            }
            .qr-code {
              max-width: 300px;
              height: auto;
            }
            .info {
              margin-top: 30px;
              text-align: left;
              max-width: 400px;
              margin-left: auto;
              margin-right: auto;
            }
            .info-row {
              display: flex;
              justify-content: space-between;
              margin-bottom: 10px;
              padding: 8px 0;
              border-bottom: 1px solid #eee;
            }
            .label {
              font-weight: bold;
              color: #333;
            }
            .value {
              color: #666;
            }
          </style>
        </head>
        <body>
          <div class="header">
            <h1>QR Code Profil Lansia</h1>
            <h2>${profileName}</h2>
          </div>
          
          <div class="qr-container">
            <img src="${qrDataURL}" class="qr-code" alt="QR Code" />
          </div>
          
          <div class="info">
            <div class="info-row">
              <span class="label">Nama:</span>
              <span class="value">${qrData?.profile?.nama || '-'}</span>
            </div>
            <div class="info-row">
              <span class="label">Usia:</span>
              <span class="value">${qrData?.profile?.usia || '-'} tahun</span>
            </div>
            <div class="info-row">
              <span class="label">Alamat:</span>
              <span class="value">${qrData?.profile?.alamat || '-'}</span>
            </div>
            <div class="info-row">
              <span class="label">No. Telepon:</span>
              <span class="value">${qrData?.profile?.no_telepon || '-'}</span>
            </div>
            <div class="info-row">
              <span class="label">Kode QR:</span>
              <span class="value">${qrData?.qr_code || '-'}</span>
            </div>
            <div class="info-row">
              <span class="label">Tanggal Dibuat:</span>
              <span class="value">${new Date().toLocaleDateString('id-ID')}</span>
            </div>
          </div>
          
          <div style="margin-top: 40px; font-size: 12px; color: #999;">
            <p>Aplikasi Kesehatan Lansia - Posyandu</p>
            <p>Scan QR code ini untuk mengakses data kesehatan</p>
          </div>
        </body>
        </html>
      `;

      const { uri } = await printToFileAsync({
        html: htmlContent,
        base64: false,
      });

      await shareAsync(uri, {
        UTI: '.pdf',
        mimeType: 'application/pdf',
      });

      Toast.show({
        type: 'success',
        text1: 'PDF Berhasil Dibuat',
        text2: 'QR code telah disimpan sebagai PDF',
      });
    } catch (error) {
      console.error('PDF generation error:', error);
      Toast.show({
        type: 'error',
        text1: 'Gagal Membuat PDF',
        text2: 'Tidak dapat membuat file PDF',
      });
    }
  };

  const handleCopyCode = async () => {
    try {
      if (!qrData?.qr_code) return;

      // Note: Clipboard is not available in this environment
      // In a real app, you would use @react-native-clipboard/clipboard
      Toast.show({
        type: 'info',
        text1: 'Kode QR',
        text2: qrData.qr_code,
      });
    } catch (error) {
      console.error('Copy error:', error);
    }
  };

  if (!visible) return null;

  return (
    <Modal visible={visible} animationType="slide" presentationStyle="pageSheet">
      <View style={[styles.container, { backgroundColor: colors.background }]}>
        <View style={styles.header}>
          <Typography variant="h3" weight="semibold">
            QR Code Generator
          </Typography>
          <Button
            title="Tutup"
            variant="ghost"
            onPress={onClose}
            style={styles.closeButton}
          />
        </View>

        <View style={styles.content}>
          {isLoading ? (
            <Card variant="outlined" style={styles.loadingCard}>
              <CardContent>
                <View style={styles.loadingContent}>
                  <Typography variant="body1" align="center">
                    Membuat QR Code...
                  </Typography>
                </View>
              </CardContent>
            </Card>
          ) : qrData ? (
            <>
              <Card variant="elevated" style={styles.qrCard}>
                <CardHeader>
                  <Heading3 align="center">{profileName}</Heading3>
                  <Typography variant="body2" align="center" color="muted">
                    QR Code Profil Kesehatan
                  </Typography>
                </CardHeader>
                <CardContent>
                  <View style={styles.qrContainer}>
                    <QRCode
                      value={qrData.qr_code}
                      size={200}
                      color={colors.text}
                      backgroundColor={colors.surface}
                      getRef={(ref: any) => setQrRef(ref)}
                    />
                  </View>
                  
                  <View style={styles.qrInfo}>
                    <Typography variant="caption" align="center" color="muted">
                      Kode: {qrData.qr_code}
                    </Typography>
                  </View>
                </CardContent>
              </Card>

              <Card variant="outlined" style={styles.profileCard}>
                <CardContent>
                  <Typography variant="h4" weight="semibold" style={styles.profileTitle}>
                    Informasi Profil
                  </Typography>
                  
                  <View style={styles.profileInfo}>
                    <View style={styles.infoRow}>
                      <Typography variant="body2" color="muted">Nama:</Typography>
                      <Typography variant="body2" weight="medium">
                        {qrData.profile?.nama || '-'}
                      </Typography>
                    </View>
                    <View style={styles.infoRow}>
                      <Typography variant="body2" color="muted">Usia:</Typography>
                      <Typography variant="body2" weight="medium">
                        {qrData.profile?.usia || '-'} tahun
                      </Typography>
                    </View>
                    <View style={styles.infoRow}>
                      <Typography variant="body2" color="muted">Alamat:</Typography>
                      <Typography variant="body2" weight="medium" numberOfLines={2}>
                        {qrData.profile?.alamat || '-'}
                      </Typography>
                    </View>
                    <View style={styles.infoRow}>
                      <Typography variant="body2" color="muted">No. Telepon:</Typography>
                      <Typography variant="body2" weight="medium">
                        {qrData.profile?.no_telepon || '-'}
                      </Typography>
                    </View>
                  </View>
                </CardContent>
              </Card>

              <View style={styles.actionButtons}>
                <Button
                  title="Bagikan"
                  onPress={handleShare}
                  icon={<Ionicons name="share" size={20} color={colors.primaryForeground} />}
                  style={styles.actionButton}
                />
                <Button
                  title="Cetak PDF"
                  variant="outline"
                  onPress={handlePrint}
                  icon={<Ionicons name="print" size={20} color={colors.primary} />}
                  style={styles.actionButton}
                />
                <Button
                  title="Salin Kode"
                  variant="ghost"
                  onPress={handleCopyCode}
                  icon={<Ionicons name="copy" size={20} color={colors.text} />}
                  style={styles.actionButton}
                />
              </View>
            </>
          ) : (
            <Card variant="outlined" style={styles.errorCard}>
              <CardContent>
                <View style={styles.errorContent}>
                  <Ionicons name="alert-circle" size={48} color={colors.error} />
                  <Typography variant="h4" align="center" style={styles.errorTitle}>
                    Gagal Membuat QR Code
                  </Typography>
                  <Typography variant="body2" align="center" color="muted">
                    Tidak dapat membuat QR code untuk profil ini
                  </Typography>
                  <Button
                    title="Coba Lagi"
                    onPress={fetchQRData}
                    style={styles.retryButton}
                  />
                </View>
              </CardContent>
            </Card>
          )}
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingTop: 60,
    paddingHorizontal: 20,
    paddingBottom: 20,
  },
  closeButton: {
    minWidth: 60,
  },
  content: {
    flex: 1,
    padding: 20,
  },
  loadingCard: {
    marginBottom: 20,
  },
  loadingContent: {
    padding: 40,
  },
  qrCard: {
    marginBottom: 20,
  },
  qrContainer: {
    alignItems: 'center',
    marginVertical: 20,
  },
  qrInfo: {
    marginTop: 16,
  },
  profileCard: {
    marginBottom: 20,
  },
  profileTitle: {
    marginBottom: 16,
  },
  profileInfo: {
    gap: 12,
  },
  infoRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    paddingVertical: 4,
  },
  actionButtons: {
    gap: 12,
  },
  actionButton: {
    marginBottom: 8,
  },
  errorCard: {
    flex: 1,
    justifyContent: 'center',
  },
  errorContent: {
    alignItems: 'center',
    gap: 16,
    padding: 20,
  },
  errorTitle: {
    marginTop: 8,
  },
  retryButton: {
    marginTop: 16,
  },
});
